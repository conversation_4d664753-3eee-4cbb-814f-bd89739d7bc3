import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProjects } from '../hooks/useProjects';
import { useProjectsRealtime } from '../hooks/useRealtime';
import { Proyecto, ProyectoFilters, ESTADOS_PROYECTO, PRIORIDADES_PROYECTO, ESTADO_COLORS } from '../types/proyecto';
import { InlineEditableCell } from '../components/UI/InlineEditableCell';
import { useInlineEdit } from '../hooks/useInlineEdit';
import { ProjectKanban } from '../components/Projects/ProjectKanban';
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Calendar,
  User,
  TrendingUp,
  RefreshCw,
  Trash2
} from 'lucide-react';

const ProjectsPage: React.FC = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'list' | 'kanban'>('list');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ProyectoFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  const {
    projects,
    loading,
    error,
    total,
    currentPage,
    pageSize,
    fetchProjects,
    updateProjectField,
    deleteProject,
    calculateDynamicProgress,
    getProgressColor,
    setError
  } = useProjects();

  // Wrapper function to match expected signature
  const updateProjectFieldWrapper = async (id: string, field: keyof Proyecto, value: any) => {
    await updateProjectField(id, field as any, value);
  };

  // Hook para edición in-place
  const {
    data: editableProjects,
    updateField,
    setData
  } = useInlineEdit({
    initialData: projects,
    updateFunction: updateProjectFieldWrapper,
    onError: (error) => {
      console.error('Error updating project:', error);
    },
    onSuccess: (id, field, value) => {
      console.log(`Project ${id} field ${String(field)} updated to:`, value);
    }
  });

  // Setup realtime updates
  useProjectsRealtime(
    () => fetchProjects(currentPage, { ...filters, search: searchTerm }),
    () => fetchProjects(currentPage, { ...filters, search: searchTerm })
  );

  // Actualizar datos editables cuando cambien los proyectos
  useEffect(() => {
    if (projects.length > 0) {
      // Calcular progreso dinámico para cada proyecto
      const projectsWithDynamicProgress = projects.map(project => ({
        ...project,
        progreso: calculateDynamicProgress(project)
      }));
      setData(projectsWithDynamicProgress);
    }
  }, [projects, calculateDynamicProgress, setData]);

  // Apply filters when they change
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchProjects(1, { ...filters, search: searchTerm });
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, fetchProjects]);

  const handleCreateProject = () => {
    navigate('/proyectos/nuevo');
  };

  const handleProjectClick = (projectId: string) => {
    navigate(`/proyectos/${projectId}`);
  };

  const handlePageChange = (page: number) => {
    fetchProjects(page, { ...filters, search: searchTerm });
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const handleDeleteProject = async (projectId: string, projectName: string) => {
    if (window.confirm(`¿Estás seguro de que quieres eliminar el proyecto "${projectName}"? Esta acción no se puede deshacer.`)) {
      try {
        await deleteProject(projectId);
        // Actualizar la lista local
        setData(prev => prev.filter(p => p.id !== projectId));
      } catch (error) {
        console.error('Error deleting project:', error);
        alert('Error al eliminar el proyecto. Por favor, inténtalo de nuevo.');
      }
    }
  };

  const getStatusColor = (estado: string) => {
    return ESTADO_COLORS[estado as keyof typeof ESTADO_COLORS] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error al cargar proyectos
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              fetchProjects(1, { ...filters, search: searchTerm });
            }}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Proyectos</h1>
            <p className="text-gray-600 mt-1">
              Gestiona todos tus proyectos en un solo lugar
            </p>
          </div>
          
          <button
            onClick={handleCreateProject}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Proyecto
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar proyectos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* View Mode and Filters */}
            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('kanban')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'kanban' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Grid className="h-4 w-4" />
                </button>
              </div>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg transition-colors ${
                  showFilters || Object.keys(filters).length > 0
                    ? 'border-blue-300 bg-blue-50 text-blue-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {Object.keys(filters).length > 0 && (
                  <span className="ml-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {Object.keys(filters).length}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estado
                  </label>
                  <select
                    value={filters.estado || ''}
                    onChange={(e) => setFilters({ ...filters, estado: e.target.value || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos los estados</option>
                    {ESTADOS_PROYECTO.map((estado) => (
                      <option key={estado} value={estado}>
                        {estado.charAt(0).toUpperCase() + estado.slice(1).replace('_', ' ')}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Prioridad
                  </label>
                  <select
                    value={filters.prioridad || ''}
                    onChange={(e) => setFilters({ ...filters, prioridad: e.target.value || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todas las prioridades</option>
                    {PRIORIDADES_PROYECTO.map((prioridad) => (
                      <option key={prioridad} value={prioridad}>
                        {prioridad}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={clearFilters}
                    className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Limpiar filtros
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-sm text-gray-600">
            Mostrando {editableProjects.length} de {total} proyectos
          </p>

          {loading && (
            <div className="flex items-center text-sm text-gray-600">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Cargando...
            </div>
          )}
        </div>

        {/* Projects List/Kanban */}
        {viewMode === 'list' ? (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {editableProjects.length === 0 ? (
              <div className="text-center py-12">
                <TrendingUp className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No se encontraron proyectos
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || Object.keys(filters).length > 0
                    ? 'Intenta ajustar los filtros de búsqueda.'
                    : 'Crea tu primer proyecto para comenzar.'}
                </p>
                {!searchTerm && Object.keys(filters).length === 0 && (
                  <button
                    onClick={handleCreateProject}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Crear Proyecto
                  </button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Proyecto
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Estado
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Progreso
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Responsable
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Fecha Fin
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tareas
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Acciones
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {editableProjects.map((project) => (
                      <tr
                        key={project.id}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-6 py-4">
                          <div>
                            <InlineEditableCell
                              value={project.nombre}
                              type="text"
                              onSave={(value) => updateField(project.id, 'nombre', value)}
                              className="text-sm font-medium text-gray-900 mb-1"
                              placeholder="Nombre del proyecto"
                              required
                            />
                            <InlineEditableCell
                              value={project.descripcion || ''}
                              type="text"
                              onSave={(value) => updateField(project.id, 'descripcion', value)}
                              className="text-sm text-gray-500"
                              placeholder="Descripción del proyecto"
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <InlineEditableCell
                            value={project.estado}
                            type="select"
                            options={ESTADOS_PROYECTO.map(estado => ({ value: estado, label: estado }))}
                            onSave={(value) => updateField(project.id, 'estado', value)}
                            renderValue={(value) => (
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(value)}`}>
                                {value}
                              </span>
                            )}
                          />
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                              <div
                                className={`h-2 rounded-full ${getProgressColor(project.progreso)}`}
                                style={{ width: `${project.progreso}%` }}
                              />
                            </div>
                            <span className="text-sm text-gray-600">{project.progreso}%</span>
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            Calculado automáticamente
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          {project.responsable_usuario ? (
                            <div className="flex items-center">
                              <User className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">
                                {project.responsable_usuario.nombre}
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Sin asignar</span>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center text-sm text-gray-900">
                            <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                            {formatDate(project.fecha_fin_estimada)}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">
                            {project.tareas_completadas || 0} / {project.total_tareas || 0}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleProjectClick(project.id)}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              title="Ver detalles"
                            >
                              Ver
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteProject(project.id, project.nombre);
                              }}
                              className="text-red-600 hover:text-red-800 p-1 rounded"
                              title="Eliminar proyecto"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <ProjectKanban
              projects={editableProjects}
              onProjectClick={handleProjectClick}
              onUpdateProject={updateField}
              onDeleteProject={handleDeleteProject}
              getProgressColor={getProgressColor}
            />
          </div>
        )}

        {/* Pagination */}
        {total > pageSize && (
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Página {currentPage} de {Math.ceil(total / pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Anterior
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= Math.ceil(total / pageSize)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Siguiente
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectsPage;
