"""
Constantes para tipos de hallazgos
Estos valores deben coincidir exactamente con el ENUM de la base de datos
"""

from typing import Literal
from enum import Enum

# Enum para tipos de hallazgos según la base de datos
class HallazgoTipo(str, Enum):
    INEFICIENCIA = "ineficiencia"
    LADRON_TIEMPO = "ladron_tiempo"
    OPORTUNIDAD_MEJORA = "oportunidad_mejora"
    RIESGO_IDENTIFICADO = "riesgo_identificado"
    DEFICIT_GOBERNANZA_DATOS = "deficit_gobernanza_datos"
    FALTA_ESTANDARIZACION = "falta_estandarizacion"
    EQUIPAMIENTO_INADECUADO = "equipamiento_inadecuado"

# Tipo Literal para validación estricta
HallazgoTipoLiteral = Literal[
    "ineficiencia",
    "ladron_tiempo", 
    "oportunidad_mejora",
    "riesgo_identificado",
    "deficit_gobernanza_datos",
    "falta_estandarizacion",
    "equipamiento_inadecuado"
]

# Lista de todos los tipos válidos
HALLAZGO_TIPOS_VALIDOS = [tipo.value for tipo in HallazgoTipo]

# Etiquetas en español para cada tipo de hallazgo
HALLAZGO_TIPO_LABELS = {
    HallazgoTipo.INEFICIENCIA: "Ineficiencia",
    HallazgoTipo.LADRON_TIEMPO: "Ladrón de Tiempo",
    HallazgoTipo.OPORTUNIDAD_MEJORA: "Oportunidad de Mejora",
    HallazgoTipo.RIESGO_IDENTIFICADO: "Riesgo Identificado",
    HallazgoTipo.DEFICIT_GOBERNANZA_DATOS: "Déficit de Gobernanza de Datos",
    HallazgoTipo.FALTA_ESTANDARIZACION: "Falta de Estandarización",
    HallazgoTipo.EQUIPAMIENTO_INADECUADO: "Equipamiento Inadecuado",
}

def get_hallazgo_tipo_label(tipo: str) -> str:
    """Obtiene la etiqueta en español para un tipo de hallazgo"""
    try:
        hallazgo_tipo = HallazgoTipo(tipo)
        return HALLAZGO_TIPO_LABELS.get(hallazgo_tipo, "Sin clasificar")
    except ValueError:
        return "Sin clasificar"

def is_valid_hallazgo_tipo(tipo: str) -> bool:
    """Valida si un tipo de hallazgo es válido"""
    return tipo in HALLAZGO_TIPOS_VALIDOS
