import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useProjects } from '../hooks/useProjects';
import { useTasks } from '../hooks/useTasks';
import { useProjectsRealtime, useTasksRealtime } from '../hooks/useRealtime';
import { Proyecto, ESTADOS_PROYECTO, PRIORIDADES_PROYECTO, ESTADO_COLORS } from '../types/proyecto';
import { Tarea, ESTADOS_TAREA, PRIORIDADES_TAREA, EstadoTarea } from '../types/tarea';
import { EditableField } from '../components/UI/EditableField';
import { InlineEditableCell } from '../components/UI/InlineEditableCell';
import {
  ArrowLeft,
  Building,
  Target,
  TrendingUp,
  Plus,
  Search,
  Trash2,
  RefreshCw
} from 'lucide-react';

const ProjectDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Proyecto | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tasksFilter, setTasksFilter] = useState('');
  const [tasksSearch, setTasksSearch] = useState('');
  const [groupBy, setGroupBy] = useState<'none' | 'estado' | 'prioridad' | 'urgencia'>('none');

  const { getProject, updateProjectField, deleteProject } = useProjects();
  const { 
    tasks, 
    loading: tasksLoading, 
    fetchTasks, 
    updateTaskField, 
    deleteTask,
    createTask 
  } = useTasks();

  // Setup realtime updates
  useProjectsRealtime(
    () => loadProject(),
    () => loadProject()
  );

  useTasksRealtime(
    () => loadTasks(),
    () => loadTasks(),
    id
  );

  const loadProject = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const projectData = await getProject(id);
      setProject(projectData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading project');
    } finally {
      setLoading(false);
    }
  };

  const loadTasks = async () => {
    if (!id) return;

    await fetchTasks(1, {
      proyecto_id: id,
      search: tasksSearch,
      estado: (tasksFilter as EstadoTarea) || undefined
    });
  };

  useEffect(() => {
    loadProject();
  }, [id]);

  useEffect(() => {
    loadTasks();
  }, [id, tasksFilter, tasksSearch]);

  const handleUpdateProject = async (field: string, value: any) => {
    if (!project) return;
    
    try {
      await updateProjectField(project.id, field as any, value);
      await loadProject(); // Reload to get updated data
    } catch (error) {
      console.error('Error updating project:', error);
      throw error;
    }
  };

  const handleDeleteProject = async () => {
    if (!project) return;
    
    if (window.confirm(`¿Estás seguro de que quieres eliminar el proyecto "${project.nombre}"? Esta acción no se puede deshacer.`)) {
      try {
        await deleteProject(project.id);
        navigate('/proyectos');
      } catch (error) {
        console.error('Error deleting project:', error);
        alert('Error al eliminar el proyecto. Por favor, inténtalo de nuevo.');
      }
    }
  };

  const handleCreateTask = async () => {
    if (!project) return;
    
    const taskData = {
      titulo: 'Nueva tarea',
      descripcion: '',
      proyecto_id: project.id,
      estado: 'Pendiente' as const,
      prioridad: 'Media' as const,
      urgencia: 'No Urgente' as const
    };

    try {
      await createTask(taskData);
      await loadTasks();
    } catch (error) {
      console.error('Error creating task:', error);
      alert('Error al crear la tarea. Por favor, inténtalo de nuevo.');
    }
  };

  const groupTasks = (tasks: Tarea[]) => {
    if (groupBy === 'none') return { 'Todas las tareas': tasks };
    
    const grouped: Record<string, Tarea[]> = {};
    
    tasks.forEach(task => {
      let key = '';
      switch (groupBy) {
        case 'estado':
          key = task.estado;
          break;
        case 'prioridad':
          key = task.prioridad;
          break;
        case 'urgencia':
          key = task.urgencia;
          break;
      }
      
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(task);
    });
    
    return grouped;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center">
          <RefreshCw className="h-6 w-6 mr-3 animate-spin text-blue-600" />
          <span className="text-lg text-gray-600">Cargando proyecto...</span>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error al cargar el proyecto
          </h2>
          <p className="text-gray-600 mb-4">{error || 'Proyecto no encontrado'}</p>
          <button
            onClick={() => navigate('/proyectos')}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Proyectos
          </button>
        </div>
      </div>
    );
  }

  const groupedTasks = groupTasks(tasks);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/proyectos')}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <EditableField
                label="Nombre del proyecto"
                value={project.nombre}
                type="text"
                onSave={(value) => handleUpdateProject('nombre', value)}
                className="text-3xl font-bold text-gray-900"
                required
              />
              <div className="flex items-center mt-2 space-x-4">
                <EditableField
                  label="Estado"
                  value={project.estado}
                  type="select"
                  options={ESTADOS_PROYECTO.map(estado => ({ 
                    value: estado, 
                    label: estado.charAt(0).toUpperCase() + estado.slice(1).replace('_', ' ')
                  }))}
                  onSave={(value) => handleUpdateProject('estado', value)}
                  renderValue={(value) => (
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${ESTADO_COLORS[value as keyof typeof ESTADO_COLORS] || 'bg-gray-100 text-gray-800'}`}>
                      {value}
                    </span>
                  )}
                />
                <EditableField
                  label="Prioridad"
                  value={project.prioridad}
                  type="select"
                  options={PRIORIDADES_PROYECTO.map(prioridad => ({ value: prioridad, label: prioridad }))}
                  onSave={(value) => handleUpdateProject('prioridad', value)}
                />
              </div>
            </div>
          </div>
          
          <button
            onClick={handleDeleteProject}
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Eliminar Proyecto
          </button>
        </div>

        {/* Main Content */}
        <div className="space-y-8">
          {/* Project Info */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Información del Proyecto</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <EditableField
                  label="Descripción"
                  value={project.descripcion || ''}
                  type="textarea"
                  onSave={(value) => handleUpdateProject('descripcion', value)}
                  placeholder="Descripción del proyecto"
                />
              </div>
              
              <div>
                <EditableField
                  label="Objetivo"
                  value={project.objetivo || ''}
                  type="textarea"
                  onSave={(value) => handleUpdateProject('objetivo', value)}
                  placeholder="Objetivo del proyecto"
                />
              </div>
              
              <div>
                <EditableField
                  label="Fecha de inicio"
                  value={project.fecha_inicio || ''}
                  type="date"
                  onSave={(value) => handleUpdateProject('fecha_inicio', value)}
                />
              </div>
              
              <div>
                <EditableField
                  label="Fecha fin estimada"
                  value={project.fecha_fin_estimada || ''}
                  type="date"
                  onSave={(value) => handleUpdateProject('fecha_fin_estimada', value)}
                />
              </div>
              
              <div>
                <EditableField
                  label="Presupuesto"
                  value={project.presupuesto || ''}
                  type="number"
                  onSave={(value) => handleUpdateProject('presupuesto', value)}
                  placeholder="0.00"
                />
              </div>
              
              <div>
                <div className="mb-2">
                  <label className="block text-sm font-medium text-gray-700">Progreso</label>
                </div>
                <div className="flex items-center">
                  <div className="flex-1 bg-gray-200 rounded-full h-3 mr-4">
                    <div
                      className={`h-3 rounded-full transition-all duration-300 bg-blue-500`}
                      style={{ width: `${project.progreso}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900">{project.progreso}%</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Calculado automáticamente basado en tareas completadas</p>
              </div>
            </div>
          </div>

          {/* Tasks Section */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  Tareas Asociadas ({tasks.length})
                </h2>
                <button
                  onClick={handleCreateTask}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Nueva Tarea
                </button>
              </div>

              {/* Task Filters */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Buscar tareas..."
                      value={tasksSearch}
                      onChange={(e) => setTasksSearch(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <select
                    value={tasksFilter}
                    onChange={(e) => setTasksFilter(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos los estados</option>
                    {ESTADOS_TAREA.map((estado) => (
                      <option key={estado} value={estado}>
                        {estado}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Agrupar por:</span>
                  <select
                    value={groupBy}
                    onChange={(e) => setGroupBy(e.target.value as any)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="none">Sin agrupar</option>
                    <option value="estado">Estado</option>
                    <option value="prioridad">Prioridad</option>
                    <option value="urgencia">Urgencia</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Tasks List */}
            <div className="p-6">
              {tasksLoading ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-6 w-6 mx-auto mb-2 animate-spin text-blue-600" />
                  <p className="text-gray-600">Cargando tareas...</p>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center py-8">
                  <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No hay tareas
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Crea la primera tarea para este proyecto.
                  </p>
                  <button
                    onClick={handleCreateTask}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Crear Tarea
                  </button>
                </div>
              ) : (
                <div className="space-y-6">
                  {Object.entries(groupedTasks).map(([groupName, groupTasks]) => (
                    <div key={groupName}>
                      {groupBy !== 'none' && (
                        <h3 className="text-md font-medium text-gray-900 mb-3 border-b border-gray-200 pb-2">
                          {groupName} ({groupTasks.length})
                        </h3>
                      )}
                      
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tarea
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Estado
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Prioridad
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Asignado
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Vencimiento
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Acciones
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {groupTasks.map((task) => (
                              <tr key={task.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4">
                                  <InlineEditableCell
                                    value={task.titulo}
                                    type="text"
                                    onSave={(value) => updateTaskField(task.id, 'titulo', value)}
                                    className="font-medium text-gray-900"
                                    required
                                  />
                                </td>
                                <td className="px-6 py-4">
                                  <InlineEditableCell
                                    value={task.estado}
                                    type="select"
                                    options={ESTADOS_TAREA.map(estado => ({ value: estado, label: estado }))}
                                    onSave={(value) => updateTaskField(task.id, 'estado', value)}
                                  />
                                </td>
                                <td className="px-6 py-4">
                                  <InlineEditableCell
                                    value={task.prioridad}
                                    type="select"
                                    options={PRIORIDADES_TAREA.map(prioridad => ({ value: prioridad, label: prioridad }))}
                                    onSave={(value) => updateTaskField(task.id, 'prioridad', value)}
                                  />
                                </td>
                                <td className="px-6 py-4">
                                  <span className="text-sm text-gray-600">
                                    {task.asignado_usuario?.nombre || 'Sin asignar'}
                                  </span>
                                </td>
                                <td className="px-6 py-4">
                                  <InlineEditableCell
                                    value={task.fecha_vencimiento || ''}
                                    type="date"
                                    onSave={(value) => updateTaskField(task.id, 'fecha_vencimiento', value)}
                                  />
                                </td>
                                <td className="px-6 py-4">
                                  <div className="flex items-center space-x-2">
                                    <button
                                      onClick={() => navigate(`/tareas/${task.id}`)}
                                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    >
                                      Ver
                                    </button>
                                    <button
                                      onClick={() => deleteTask(task.id)}
                                      className="text-red-600 hover:text-red-800 p-1 rounded"
                                      title="Eliminar tarea"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Related Entities */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Associated Companies */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Building className="h-5 w-5 mr-2" />
                Empresas Asociadas
              </h3>
              {project.empresas_asociadas && project.empresas_asociadas.length > 0 ? (
                <div className="space-y-2">
                  {project.empresas_asociadas.map((empresa) => (
                    <div key={empresa.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{empresa.nombre}</p>
                        <p className="text-sm text-gray-600">{empresa.tipo_relacion}</p>
                      </div>
                      <button
                        onClick={() => navigate(`/empresas/${empresa.id}`)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Ver detalles
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No hay empresas asociadas</p>
              )}
            </div>

            {/* Project Statistics */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Estadísticas
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total de tareas:</span>
                  <span className="font-medium">{project.total_tareas || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tareas completadas:</span>
                  <span className="font-medium">{project.tareas_completadas || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Progreso:</span>
                  <span className="font-medium">{project.progreso}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Responsable:</span>
                  <span className="font-medium">
                    {project.responsable_usuario?.nombre || 'Sin asignar'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Fecha de creación:</span>
                  <span className="font-medium">{formatDate(project.created_at)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Última actualización:</span>
                  <span className="font-medium">{formatDate(project.updated_at)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetailsPage;
